// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import type { CompetitionFormatService } from './competition-formats.class'

// Main data model schema
export const competitionFormatSchema = Type.Object(
  {
    id: Type.Number(),
    legacyId: Type.Optional(Type.Number()),
    active: Type.Boolean(),
    name: Type.String(),
    numberOfShots: Type.Optional(Type.Number()),
    typesOfTargets: Type.Optional(Type.String()),
    targets: Type.Optional(Type.Number()),
    scoring: Type.Optional(Type.Unknown()), // JSONB array of scoring values
    matchType: Type.Optional(Type.String()),
    federationId: Type.Optional(Type.Number()),
    federation: Type.Optional(Type.Unknown()), // Virtual field for federation relation
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number())
  },
  { $id: 'CompetitionFormat', additionalProperties: false }
)
export type CompetitionFormat = Static<typeof competitionFormatSchema>
export const competitionFormatValidator = getValidator(competitionFormatSchema, dataValidator)
export const competitionFormatResolver = resolve<CompetitionFormat, HookContext<CompetitionFormatService>>({
  // Add a virtual field to load the related federation
  federation: virtual(async (competitionFormat: CompetitionFormat, context: HookContext<CompetitionFormatService>) => {
    if (competitionFormat.federationId) {
      return context.app.service('federations').get(competitionFormat.federationId)
    }
    return undefined
  })
})

export const competitionFormatExternalResolver = resolve<CompetitionFormat, HookContext<CompetitionFormatService>>({})

// Schema for creating new entries
export const competitionFormatDataSchema = Type.Pick(
  competitionFormatSchema,
  [
    'legacyId',
    'active',
    'name',
    'numberOfShots',
    'typesOfTargets',
    'targets',
    'scoring',
    'matchType',
    'federationId'
  ],
  {
    $id: 'CompetitionFormatData'
  }
)
export type CompetitionFormatData = Static<typeof competitionFormatDataSchema>
export const competitionFormatDataValidator = getValidator(competitionFormatDataSchema, dataValidator)
export const competitionFormatDataResolver = resolve<CompetitionFormat, HookContext<CompetitionFormatService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const competitionFormatPatchSchema = Type.Partial(competitionFormatSchema, {
  $id: 'CompetitionFormatPatch'
})
export type CompetitionFormatPatch = Static<typeof competitionFormatPatchSchema>
export const competitionFormatPatchValidator = getValidator(competitionFormatPatchSchema, dataValidator)
export const competitionFormatPatchResolver = resolve<CompetitionFormat, HookContext<CompetitionFormatService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const competitionFormatQueryProperties = Type.Pick(
  competitionFormatSchema,
  [
    'id',
    'legacyId',
    'active',
    'name',
    'numberOfShots',
    'typesOfTargets',
    'targets',
    'matchType',
    'federationId'
  ]
)
export const competitionFormatQuerySchema = Type.Intersect(
  [
    querySyntax(competitionFormatQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type CompetitionFormatQuery = Static<typeof competitionFormatQuerySchema>
export const competitionFormatQueryValidator = getValidator(competitionFormatQuerySchema, queryValidator)
export const competitionFormatQueryResolver = resolve<CompetitionFormatQuery, HookContext<CompetitionFormatService>>({})
