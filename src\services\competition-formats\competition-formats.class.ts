// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { CompetitionFormat, CompetitionFormatData, CompetitionFormatPatch, CompetitionFormatQuery } from './competition-formats.schema'

export type { CompetitionFormat, CompetitionFormatData, CompetitionFormatPatch, CompetitionFormatQuery }

export interface CompetitionFormatParams extends KnexAdapterParams<CompetitionFormatQuery> { }

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class CompetitionFormatService<ServiceParams extends Params = CompetitionFormatParams> extends KnexService<
    CompetitionFormat,
    CompetitionFormatData,
    CompetitionFormatParams,
    CompetitionFormatPatch
> { }

export const getOptions = (app: Application): KnexAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        Model: app.get('postgresqlClient'),
        name: 'competition_formats'
    }
}
