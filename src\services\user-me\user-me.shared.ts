// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { UserMeServiceMethods } from './user-me.class'

// Define the UserProfile type based on the service implementation
export interface UserProfile {
  id: number
  email: string
  avatar?: string
  googleId?: string
  facebookId?: string
  githubId?: string
  players?: Array<{
    id: number
    firstname?: string
    lastname?: string
    address?: string
    zipcode?: string
    city?: string
    country?: string
    phone?: string
    birthdate?: string
    sex?: string
    latitude?: number
    longitude?: number
    equipmentCategory?: string
    avatar?: string
    licenses?: any[]
  }>
  organizers?: Array<{
    id: number
    firstname?: string
    lastname?: string
    address?: string
    zipcode?: string
    city?: string
    country?: string
    phone?: string
    birthdate?: string
    sex?: string
    latitude?: number
    longitude?: number
    equipmentCategory?: string
    avatar?: string
    licenses?: any[]
  }>
  equipment?: Array<any>
}

export type UserMeClientService = Pick<UserMeServiceMethods, 'find' | 'patch'>

export const userMePath = 'users/me'

export const userMeMethods: Array<keyof UserMeServiceMethods> = ['find', 'patch']

export const userMeClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(userMePath, connection.service(userMePath), {
    methods: userMeMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [userMePath]: UserMeClientService
  }
}
