// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { CompetitionFormat, CompetitionFormatData, CompetitionFormatPatch, CompetitionFormatQuery, CompetitionFormatService } from './competition-formats.class'

export type { CompetitionFormat, CompetitionFormatData, CompetitionFormatPatch, CompetitionFormatQuery }

export type CompetitionFormatClientService = Pick<CompetitionFormatService<Params<CompetitionFormatQuery>>, (typeof competitionFormatsMethods)[number]>

export const competitionFormatsPath = 'competition-formats'

export const competitionFormatsMethods: Array<keyof CompetitionFormatService> = ['find', 'get', 'create', 'patch', 'remove']

export const competitionFormatsClient = (client: ClientApplication) => {
    const connection = client.get('connection')

    client.use(competitionFormatsPath, connection.service(competitionFormatsPath), {
        methods: competitionFormatsMethods
    })
}

// Add this service to the client service type index
declare module '../../client' {
    interface ServiceTypes {
        [competitionFormatsPath]: CompetitionFormatClientService
    }
}
