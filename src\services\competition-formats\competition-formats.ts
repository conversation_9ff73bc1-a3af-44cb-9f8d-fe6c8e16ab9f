import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { CompetitionFormatService, getOptions } from './competition-formats.class'
import {
  competitionFormatDataResolver,
  competitionFormatDataValidator,
  competitionFormatExternalResolver,
  competitionFormatPatchResolver,
  competitionFormatPatchValidator,
  competitionFormatQueryResolver,
  competitionFormatQueryValidator,
  competitionFormatResolver
} from './competition-formats.schema'
import {
  competitionFormatDataSchema,
  competitionFormatPatchSchema,
  competitionFormatQuerySchema,
  competitionFormatSchema
} from './competition-formats.schema'
import { competitionFormatsMethods, competitionFormatsPath } from './competition-formats.shared'

export * from './competition-formats.class'
export * from './competition-formats.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const competitionFormats = (app: Application) => {
  // Register our service on the Feathers application
  app.use(competitionFormatsPath, new CompetitionFormatService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: competitionFormatsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { competitionFormatDataSchema, competitionFormatQuerySchema, competitionFormatSchema, competitionFormatPatchSchema },
      docs: {
        description: 'Competition formats service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(competitionFormatsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(competitionFormatExternalResolver),
        schemaHooks.resolveResult(competitionFormatResolver)
      ]
    },
    before: {
      all: [
        schemaHooks.validateQuery(competitionFormatQueryValidator),
        schemaHooks.resolveQuery(competitionFormatQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(competitionFormatDataValidator),
        schemaHooks.resolveData(competitionFormatDataResolver)
      ],
      patch: [
        schemaHooks.validateData(competitionFormatPatchValidator),
        schemaHooks.resolveData(competitionFormatPatchResolver)
      ],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [competitionFormatsPath]: CompetitionFormatService
  }
}
